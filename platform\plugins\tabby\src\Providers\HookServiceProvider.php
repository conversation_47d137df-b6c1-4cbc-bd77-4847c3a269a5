<?php

namespace <PERSON><PERSON><PERSON>\Tabby\Providers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON><PERSON>\Payment\Facades\PaymentMethods;
use Bo<PERSON><PERSON>\Tabby\Forms\TabbyPaymentMethodForm;
use Botble\Tabby\Services\Gateways\TabbyPaymentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerTabbyMethod'], 16, 2);
        add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithTabby'], 16, 2);

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 97);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['TABBY'] = TABBY_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == TABBY_PAYMENT_METHOD_NAME) {
                $value = 'Tabby Pay-in-4';
            }

            return $value;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == TABBY_PAYMENT_METHOD_NAME) {
                $value = Html::tag('span', 'Tabby Pay-in-4', [
                    'class' => 'label-success status-label',
                ])->toHtml();
            }

            return $value;
        }, 21, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . TabbyPaymentMethodForm::create()->renderForm();
    }

    public function registerTabbyMethod(?string $html, array $data): string
    {
        $paymentService = new TabbyPaymentService();

        // Check if Tabby is configured
        if (!$paymentService->isAvailable()) {
            return $html;
        }

        $data['errorMessage'] = null;
        $data['isEligible'] = false;
        $data['rejectionReason'] = null;

        // Perform pre-scoring check
        try {
            $preScoreResponse = $paymentService->performPreScoringCheck($data);

            if ($preScoreResponse['status'] === 'created') {
                $data['isEligible'] = true;
            } else {
                $rejectionReason = $preScoreResponse['configuration']['products']['installments']['rejection_reason'] ?? 'not_available';
                $data['rejectionReason'] = $rejectionReason;
                $data['rejectionMessage'] = $paymentService->getRejectionMessage($rejectionReason);
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            $data['errorMessage'] = $exception->getMessage();
        }

        PaymentMethods::method(TABBY_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/tabby::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithTabby(array $data, Request $request): array
    {
        if ($data['type'] !== TABBY_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $paymentService = new TabbyPaymentService();
        $supportedCurrencies = $paymentService->supportedCurrencyCodes();

        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        // Check currency support
        if (!in_array($paymentData['currency'], $supportedCurrencies)) {
            $data['error'] = true;
            $data['message'] = __(
                ":name doesn't support :currency. List of currencies supported by :name: :currencies.",
                [
                    'name' => 'Tabby Pay-in-4',
                    'currency' => $paymentData['currency'],
                    'currencies' => implode(', ', $supportedCurrencies),
                ]
            );

            return $data;
        }

        try {
            // Create checkout session
            $checkoutData = [
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'],
                'description' => $paymentData['description'],
                'order_id' => $paymentData['order_id'],
                'customer_id' => $paymentData['customer_id'],
                'customer_name' => $paymentData['address']['first_name'] . ' ' . $paymentData['address']['last_name'],
                'customer_email' => $paymentData['address']['email'],
                'customer_phone' => $paymentData['address']['phone'],
                'shipping_city' => $paymentData['address']['city'],
                'shipping_address' => $paymentData['address']['address'],
                'shipping_zip' => $paymentData['address']['zip_code'],
                'checkout_token' => $paymentData['checkout_token'],
                'return_url' => $paymentData['return_url'],
                'cancel_url' => $paymentData['cancel_url'],
                'products' => $this->formatProducts($paymentData),
                'tax_amount' => $paymentData['tax_amount'] ?? 0,
                'shipping_amount' => $paymentData['shipping_amount'] ?? 0,
                'discount_amount' => $paymentData['discount_amount'] ?? 0,
            ];

            do_action('payment_before_making_api_request', TABBY_PAYMENT_METHOD_NAME, $checkoutData);

            $response = $paymentService->createCheckoutSession($checkoutData);

            do_action('payment_after_api_response', TABBY_PAYMENT_METHOD_NAME, $checkoutData, $response);

            if ($response['status'] === 'created') {
                $webUrl = $response['configuration']['available_products']['installments'][0]['web_url'] ?? '';

                if ($webUrl) {
                    // Store payment ID for later reference
                    $data['charge_id'] = $response['payment']['id'];

                    // Redirect to Tabby checkout
                    $paymentService->redirectToCheckoutPage(['web_url' => $webUrl]);
                } else {
                    $data['error'] = true;
                    $data['message'] = __('Unable to create Tabby checkout session.');
                }
            } else {
                // Handle rejection
                $rejectionReason = $response['configuration']['products']['installments']['rejection_reason'] ?? 'not_available';
                $data['error'] = true;
                $data['message'] = $paymentService->getRejectionMessage($rejectionReason);
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            $data['error'] = true;
            $data['message'] = __('Payment failed! Please try again.');
        }

        return $data;
    }

    protected function formatProducts(array $paymentData): array
    {
        $products = [];

        if (!empty($paymentData['products'])) {
            foreach ($paymentData['products'] as $product) {
                $products[] = [
                    'id' => $product['id'] ?? '',
                    'name' => $product['name'] ?? '',
                    'description' => $product['description'] ?? '',
                    'qty' => $product['qty'] ?? 1,
                    'price' => $product['price'] ?? 0,
                    'discount_amount' => $product['discount_amount'] ?? 0,
                    'image' => $product['image'] ?? '',
                    'url' => $product['url'] ?? '',
                    'category' => $product['category'] ?? '',
                    'brand' => $product['brand'] ?? '',
                ];
            }
        }

        return $products;
    }
}
