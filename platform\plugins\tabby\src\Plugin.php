<?php

namespace Bo<PERSON>ble\Tabby;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use Bo<PERSON><PERSON>\Setting\Facades\Setting;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Setting::delete([
            'payment_tabby_status',
            'payment_tabby_public_key',
            'payment_tabby_secret_key',
            'payment_tabby_merchant_code',
            'payment_tabby_environment',
        ]);
    }
}
