<?php

return [
    'name' => 'ERPNext',
    'create' => 'New quote',
    'erpnext_form' => 'ERPNext Form',
    'erpnext' => 'ERPNext ',
    'settings' => [
        'title' => 'ERPNext Settings',
        'description' => 'View and update ERPNext settings',
        'enable_erpnext' => 'Enabled ERPNext API',
        'enable_erpnext_helper_text' => 'Enable this to integrate orders with ERPNext',
        'erpnext_url' => 'ERPNext URL',
        'erpnext_url_helper_text' => 'Enter the URL of your ERPNext instance',
    ],
    'product_import' => [
        'title' => 'Import Products',
        'description' => 'Import products from ERPNext Item Groups',
        'select_item_groups' => 'Select Item Groups',
        'preview_products' => 'Preview Products',
        'import_products' => 'Import Products',
        'loading_item_groups' => 'Loading Item Groups...',
        'loading_products' => 'Loading products...',
        'importing_products' => 'Importing products...',
        'no_item_groups' => 'No Item Groups found',
        'no_products' => 'No products found in selected Item Groups',
        'import_success' => 'Products imported successfully',
        'import_error' => 'Error importing products',
        'duplicate_sku' => 'Product with this SKU already exists',
        'instructions' => [
            'title' => 'Product Import Instructions',
            'step1' => 'Select one or more Item Groups from ERPNext to import products from',
            'step2' => 'Click "Preview Products" to see what products will be imported',
            'step3' => 'Products with existing SKUs will be skipped to prevent duplicates',
            'step4' => 'New product categories will be created automatically based on Item Groups',
            'step5' => 'Click "Import Products" to start the import process',
        ],
        'results' => [
            'total' => 'Total Products',
            'imported' => 'Successfully Imported',
            'skipped' => 'Skipped (Duplicates)',
            'errors' => 'Errors',
            'details' => 'Import Details',
        ],
    ]
];
