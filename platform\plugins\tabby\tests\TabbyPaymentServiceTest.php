<?php

namespace Bo<PERSON><PERSON>\Tabby\Tests;

use Bo<PERSON>ble\Tabby\Services\Gateways\TabbyPaymentService;
use Bo<PERSON>ble\Tabby\Services\TabbyApiService;
use PHPUnit\Framework\TestCase;
use Mockery;

class TabbyPaymentServiceTest extends TestCase
{
    protected TabbyPaymentService $paymentService;
    protected $mockApiService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockApiService = Mockery::mock(TabbyApiService::class);
        $this->paymentService = new TabbyPaymentService();
        
        // Inject mock API service
        $reflection = new \ReflectionClass($this->paymentService);
        $property = $reflection->getProperty('tabbyApiService');
        $property->setAccessible(true);
        $property->setValue($this->paymentService, $this->mockApiService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testSupportedCurrencyCodes()
    {
        $this->mockApiService
            ->shouldReceive('getSupportedCurrencies')
            ->once()
            ->andReturn(['AED', 'SAR', 'KWD', 'BHD', 'QAR', 'EGP']);

        $currencies = $this->paymentService->supportedCurrencyCodes();
        
        $this->assertIsArray($currencies);
        $this->assertContains('AED', $currencies);
        $this->assertContains('SAR', $currencies);
    }

    public function testGetAmount()
    {
        $amount = $this->paymentService->getAmount(100.50);
        $this->assertEquals('100.50', $amount);
        
        $amount = $this->paymentService->getAmount(100);
        $this->assertEquals('100.00', $amount);
    }

    public function testGetRejectionMessage()
    {
        $message = $this->paymentService->getRejectionMessage('order_amount_too_high');
        $this->assertStringContainsString('spending limit', $message);
        
        $message = $this->paymentService->getRejectionMessage('order_amount_too_low');
        $this->assertStringContainsString('minimum amount', $message);
        
        $message = $this->paymentService->getRejectionMessage('unknown_reason');
        $this->assertStringContainsString('unable to approve', $message);
    }

    public function testCreateCheckoutSessionSuccess()
    {
        $testData = [
            'amount' => 100.00,
            'currency' => 'AED',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'order_id' => 'ORDER123',
        ];

        $expectedResponse = [
            'status' => 'created',
            'payment' => ['id' => 'payment_123'],
            'configuration' => [
                'available_products' => [
                    'installments' => [
                        ['web_url' => 'https://checkout.tabby.ai/session123']
                    ]
                ]
            ]
        ];

        $this->mockApiService
            ->shouldReceive('createCheckoutSession')
            ->once()
            ->andReturn($expectedResponse);

        $response = $this->paymentService->createCheckoutSession($testData);
        
        $this->assertEquals('created', $response['status']);
        $this->assertArrayHasKey('payment', $response);
    }

    public function testPerformPreScoringCheckRejected()
    {
        $testData = [
            'amount' => 10000.00, // High amount that might be rejected
            'currency' => 'AED',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ];

        $expectedResponse = [
            'status' => 'rejected',
            'configuration' => [
                'products' => [
                    'installments' => [
                        'rejection_reason' => 'order_amount_too_high'
                    ]
                ]
            ]
        ];

        $this->mockApiService
            ->shouldReceive('createCheckoutSession')
            ->once()
            ->andReturn($expectedResponse);

        $response = $this->paymentService->performPreScoringCheck($testData);
        
        $this->assertEquals('rejected', $response['status']);
        $this->assertEquals('order_amount_too_high', 
            $response['configuration']['products']['installments']['rejection_reason']);
    }

    public function testCapturePayment()
    {
        $paymentId = 'payment_123';
        $amount = 100.00;
        $referenceId = 'ORDER123';

        $expectedResponse = [
            'status' => 'CLOSED',
            'captures' => [
                [
                    'id' => 'capture_123',
                    'amount' => '100.00'
                ]
            ]
        ];

        $this->mockApiService
            ->shouldReceive('capturePayment')
            ->once()
            ->with($paymentId, ['amount' => '100.00', 'reference_id' => $referenceId])
            ->andReturn($expectedResponse);

        $response = $this->paymentService->capturePayment($paymentId, $amount, $referenceId);
        
        $this->assertEquals('CLOSED', $response['status']);
        $this->assertArrayHasKey('captures', $response);
    }

    public function testRefundPayment()
    {
        $paymentId = 'payment_123';
        $amount = 50.00;
        $reason = 'Customer request';
        $referenceId = 'REFUND123';

        $expectedResponse = [
            'refunds' => [
                [
                    'id' => 'refund_123',
                    'amount' => '50.00',
                    'reason' => 'Customer request'
                ]
            ]
        ];

        $this->mockApiService
            ->shouldReceive('refundPayment')
            ->once()
            ->with($paymentId, [
                'amount' => '50.00',
                'reason' => $reason,
                'reference_id' => $referenceId
            ])
            ->andReturn($expectedResponse);

        $response = $this->paymentService->refundPayment($paymentId, $amount, $reason, $referenceId);
        
        $this->assertArrayHasKey('refunds', $response);
        $this->assertEquals('50.00', $response['refunds'][0]['amount']);
    }
}
