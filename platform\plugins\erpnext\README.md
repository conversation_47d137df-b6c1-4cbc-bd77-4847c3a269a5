# ERPNext Integration Plugin

This plugin provides comprehensive integration between your Laravel e-commerce application and ERPNext ERP system.

## Features

### Order Integration
- Automatic customer creation in ERPNext when orders are placed
- Sales order creation in ERPNext for each e-commerce order
- Real-time synchronization of order data

### Product Import (NEW)
- Import products from ERPNext Item Groups
- Selective import based on product categories
- Duplicate prevention using SKU matching
- Automatic category creation based on Item Groups
- Comprehensive import tracking and reporting

## Installation

1. The plugin is already installed in your Laravel application
2. Configure ERPNext API credentials in the admin settings
3. Enable the ERPNext integration

## Configuration

### ERPNext API Setup

1. Go to **Admin > Settings > E-commerce > ERPNext Settings**
2. Enable ERPNext API integration
3. Configure the following settings:
   - **ERPNext URL**: Your ERPNext instance URL (e.g., `https://your-erpnext.com`)
   - **API Key**: Generated from ERPNext user settings
   - **API Secret**: Generated from ERPNext user settings
   - **Warehouse**: Default warehouse for inventory management

### ERPNext API Credentials

To generate API credentials in ERPNext:

1. Go to User list in ERPNext
2. Open the user you want to use for API access
3. Click on "Settings" tab
4. Expand "API Access" section
5. Click "Generate Keys"
6. Copy the API Key and API Secret

## Product Import Usage

### Accessing Product Import

1. Navigate to **Admin > E-commerce > ERPNext > Import Products**
2. The system will automatically load available Item Groups from ERPNext

### Import Process

1. **Select Item Groups**: Choose one or more Item Groups from the dropdown
2. **Preview Products**: Click "Preview Products" to see what will be imported
3. **Review**: Check the product list and verify the data
4. **Import**: Click "Import Products" to start the import process

### Import Features

- **Duplicate Prevention**: Products with existing SKUs are automatically skipped
- **Category Mapping**: ERPNext Item Groups are mapped to local Product Categories
- **Automatic Category Creation**: New categories are created if they don't exist
- **Import Tracking**: All imports are logged with detailed results
- **Error Handling**: Comprehensive error reporting and logging

### Import Results

After import completion, you'll see:
- **Total Products**: Number of products found in selected Item Groups
- **Successfully Imported**: Products successfully created
- **Skipped**: Products skipped due to duplicate SKUs
- **Errors**: Products that failed to import with error details

## API Endpoints Used

The plugin uses the following ERPNext API endpoints:

- `GET /api/resource/Item Group` - Fetch Item Groups/Categories
- `GET /api/resource/Item` - Fetch Products with filtering
- `POST /api/resource/Customer` - Create customers
- `POST /api/resource/Sales Order` - Create sales orders

## Database Schema

### Product Import Tracking

The plugin creates a tracking table `erpnext_product_imports` with the following fields:

- `product_id`: Local product ID
- `erpnext_item_code`: ERPNext item code (SKU)
- `erpnext_item_name`: ERPNext item name
- `erpnext_item_group`: ERPNext item group
- `erpnext_standard_rate`: ERPNext standard rate
- `import_status`: Import status (imported, updated, failed)
- `import_data`: Original ERPNext data (JSON)
- `imported_at`: Import timestamp
- `imported_by`: User who performed the import

## Logging

All ERPNext operations are logged using the ERPNextLogger service:

- API requests and responses
- Import operations and results
- Error conditions and debugging information

Logs can be found in the Laravel log files with the `erpnext` context.

## Permissions

The plugin defines the following permissions:

- `erpnext.settings`: Access to ERPNext settings
- `erpnext.product-import`: Access to product import functionality

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Verify ERPNext URL is correct and accessible
   - Check API credentials are valid
   - Ensure ERPNext instance is running

2. **No Item Groups Found**
   - Verify API credentials have proper permissions
   - Check if Item Groups exist in ERPNext
   - Review ERPNext API logs

3. **Import Errors**
   - Check product data completeness in ERPNext
   - Verify required fields are present
   - Review import logs for specific errors

### Debug Mode

Enable debug mode in Laravel to see detailed error messages and API responses.

## Development

### Running Tests

```bash
php artisan test platform/plugins/erpnext/tests/
```

### Adding New Features

1. Follow the existing code structure and patterns
2. Add appropriate logging using ERPNextLogger
3. Include comprehensive error handling
4. Write tests for new functionality
5. Update documentation

## Support

For issues and feature requests, please check the application logs and ERPNext API documentation.

## Version History

- **v1.0.0**: Initial release with order integration
- **v1.1.0**: Added product import functionality with Item Group selection and duplicate prevention
