<?php

use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Shaqi\ERPNext\Http\Controllers'], function () {

    AdminHelper::registerRoutes(function () {

        Route::group(['prefix' => 'settings'], function (): void {
            Route::get('erpnext', [
                'as' => 'erpnext.settings',
                'uses' => 'Settings\ERPNextSettingController@edit',
            ]);

            Route::put('erpnext', [
                'as' => 'erpnext.settings.update',
                'uses' => 'Settings\ERPNextSettingController@update',
                'permission' => 'erpnext.settings',
            ]);
        });

        // Product Import Routes
        Route::group(['prefix' => 'erpnext'], function (): void {
            Route::get('product-import', [
                'as' => 'erpnext.product-import.index',
                'uses' => 'ProductImportController@index',
                'permission' => 'erpnext.product-import',
            ]);

            Route::get('product-import/item-groups', [
                'as' => 'erpnext.product-import.item-groups',
                'uses' => 'ProductImportController@getItemGroups',
                'permission' => 'erpnext.product-import',
            ]);

            Route::post('product-import/preview', [
                'as' => 'erpnext.product-import.preview',
                'uses' => 'ProductImportController@previewProducts',
                'permission' => 'erpnext.product-import',
            ]);

            Route::post('product-import/import', [
                'as' => 'erpnext.product-import.import',
                'uses' => 'ProductImportController@importProducts',
                'permission' => 'erpnext.product-import',
            ]);

            Route::get('product-import/status', [
                'as' => 'erpnext.product-import.status',
                'uses' => 'ProductImportController@getImportStatus',
                'permission' => 'erpnext.product-import',
            ]);
        });

    });
});
