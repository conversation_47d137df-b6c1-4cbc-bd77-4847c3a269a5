<?php

namespace Shaqi\ERPNext\Services;

use Illuminate\Support\Facades\Http;
use <PERSON>haqi\ERPNext\Services\ERPNextLogger;

class ERPNextService
{

    protected static function getHeaders()
    {
        return [
            'Authorization' => 'token ' . trim(setting('erpnext_api_key')) . ':' . trim(setting('erpnext_api_secret')),
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Get all Item Groups from ERPNext
     */
    public static function getItemGroups()
    {
        $url = rtrim(setting('erpnext_api_url')) . "/api/resource/Item Group";

        ERPNextLogger::logApiRequest('GET', $url, []);

        $response = Http::withHeaders(self::getHeaders())->get($url, [
            'fields' => json_encode(['name', 'item_group_name', 'parent_item_group', 'is_group']),
            'limit_page_length' => 999
        ]);

        ERPNextLogger::logApiResponse('GET', $url, $response->status(), $response->json());

        if ($response->successful() && isset($response['data'])) {
            ERPNextLogger::info("Successfully fetched Item Groups", [
                'count' => count($response['data'])
            ]);
            return $response['data'];
        }

        ERPNextLogger::error("Failed to fetch Item Groups", [
            'status' => $response->status(),
            'response' => $response->body()
        ]);
        return [];
    }

    /**
     * Get products from ERPNext by Item Group
     */
    public static function getProductsByItemGroup($itemGroupName)
    {
        $url = rtrim(setting('erpnext_api_url')) . "/api/resource/Item";
        $filters = json_encode([["item_group", "=", $itemGroupName]]);

        ERPNextLogger::logApiRequest('GET', $url, ['filters' => $filters]);

        $response = Http::withHeaders(self::getHeaders())->get($url, [
            'filters' => $filters,
            'fields' => json_encode([
                'name', 'item_name', 'item_code', 'item_group', 'description',
                'standard_rate', 'stock_uom', 'image', 'brand', 'disabled'
            ]),
            'limit_page_length' => 999
        ]);

        ERPNextLogger::logApiResponse('GET', $url, $response->status(), $response->json());

        if ($response->successful() && isset($response['data'])) {
            ERPNextLogger::info("Successfully fetched products for Item Group", [
                'item_group' => $itemGroupName,
                'count' => count($response['data'])
            ]);
            return $response['data'];
        }

        ERPNextLogger::error("Failed to fetch products for Item Group", [
            'item_group' => $itemGroupName,
            'status' => $response->status(),
            'response' => $response->body()
        ]);
        return [];
    }

    /**
     * Get products from multiple Item Groups
     */
    public static function getProductsByItemGroups(array $itemGroupNames)
    {
        $allProducts = [];

        foreach ($itemGroupNames as $itemGroupName) {
            $products = self::getProductsByItemGroup($itemGroupName);
            $allProducts = array_merge($allProducts, $products);
        }

        return $allProducts;
    }


     public static function getCustomerByEmail($customerEmail)
     {
        $url = rtrim(setting('erpnext_api_url')) . "/api/resource/Customer";
        $filters = json_encode([["email_id", "=", $customerEmail]]);

        ERPNextLogger::logApiRequest('GET', $url, ['filters' => $filters]);

        $response = Http::withHeaders(self::getHeaders())->get($url, ["filters" => $filters]);

        ERPNextLogger::logApiResponse('GET', $url, $response->status(), $response->json());

        if ($response->successful() && isset($response['data'][0])) {
            $customerId = $response['data'][0]['name'];
            ERPNextLogger::logCustomerOperation('get_by_email', $customerEmail, $customerId);
            return $customerId;
        }

        ERPNextLogger::logCustomerOperation('get_by_email', $customerEmail, null, 'Customer not found');
        return null;
     }


     public static function getCustomer($customerName)
     {
         $filters = [
             ["Customer", "customer_name", "=", $customerName]
         ];
         $query = http_build_query(['filters' => json_encode($filters)]);
         $url = setting('erpnext_api_url') . "/api/resource/Customer?" . $query;

         $response = Http::withHeaders(self::getHeaders())->get($url);

         if ($response->successful() && isset($response['data'][0])) {
             return $response['data'][0]['name'];
         }

         return null;
     }

     public static function createCustomer($name, $email, $phone)
     {
         $url = setting('erpnext_api_url'). "/api/resource/Customer";
         $payload = [
            "customer_name" => $name,
            "customer_type" => "Individual",
            "customer_group" => "Commercial",
            "territory" => "All Territories",
            "email_id" => $email,
            "mobile_no" => $phone
        ];

        ERPNextLogger::logApiRequest('POST', $url, $payload);

        $response = Http::withHeaders(self::getHeaders())->post($url, $payload);

        ERPNextLogger::logApiResponse('POST', $url, $response->status(), $response->json());

        if ($response->successful() && isset($response['data']['name'])) {
            $customerId = $response['data']['name'];
            ERPNextLogger::logCustomerOperation('create', $email, $customerId);
            return $customerId;
        }

        $errorMessage = $response->failed() ? $response->body() : 'Unknown error';
        ERPNextLogger::logCustomerOperation('create', $email, null, $errorMessage);
        return null;
     }

    public static function createSalesOrder($customerID, $items, $order)
    {
        $url = setting('erpnext_api_url') . '/api/resource/Sales Order';

        $data = [
            "customer" => $customerID,
            "transaction_date" => now()->toDateString(),
            "grand_total" => (float)$order->amount,
            "delivery_date" => now()->addDays(5)->toDateString(),
            "items" => $items,
            "docstatus" => 1
        ];

        ERPNextLogger::logApiRequest('POST', $url, $data);

        // ERPNext API Call
        $response = Http::withHeaders(self::getHeaders())->post($url, $data);

        ERPNextLogger::logApiResponse('POST', $url, $response->status(), $response->json());

        if ($response->successful()) {
            $responseData = $response->json();
            $salesOrderId = $responseData['data']['name'] ?? null;

            if ($salesOrderId) {
                ERPNextLogger::logSalesOrderOperation('create', $order->id, $customerID, $salesOrderId);
                return [
                    'success' => true,
                    'sales_order_id' => $salesOrderId,
                    'data' => $responseData
                ];
            } else {
                ERPNextLogger::logSalesOrderOperation('create', $order->id, $customerID, null, 'Sales order ID not found in response');
                return [
                    'success' => false,
                    'error' => 'Sales order ID not found in response',
                    'data' => $responseData
                ];
            }
        } else {
            $errorMessage = $response->body();
            ERPNextLogger::logSalesOrderOperation('create', $order->id, $customerID, null, $errorMessage);
            return [
                'success' => false,
                'error' => $errorMessage,
                'status_code' => $response->status()
            ];
        }
    }
}
