<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ERPNext Product Import</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5><i class="ti ti-check-circle"></i> Success!</h5>
                        <p>ERPNext Product Import page is working correctly!</p>
                        <p>Menu item is accessible and controller is functioning.</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>Next Steps:</h6>
                        <ul>
                            <li>Configure ERPNext API credentials in settings</li>
                            <li>Enable ERPNext integration</li>
                            <li>Test API connection</li>
                            <li>Start importing products</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <a href="<?php echo e(route('erpnext.settings')); ?>" class="btn btn-primary">
                                <i class="ti ti-settings"></i> ERPNext Settings
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary">
                                <i class="ti ti-package"></i> View Products
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make(BaseHelper::getAdminMasterLayoutTemplate(), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\martfury\platform/plugins/erpnext/resources/views/product-import/test.blade.php ENDPATH**/ ?>