    <ol>
        <li>
            <p>
                <a
                    href="https://accept.paymob.com/portal2/en/register"
                    target="_blank"
                >
                    <?php echo e(__('Register an account on :name', ['name' => 'Paymob'])); ?>

                </a>
            </p>
        </li>
        <li>
            <p>
                <?php echo __('Please, ensure using the secret and public keys that are exists in Paymob <a href="https://accept.paymob.com/portal2/en/settings" target="blank">merchant account</a>. For testing purposes, you can use secret and public test keys that exist in the same account'); ?>

            </p>
        </li>
        <li>
            <p>
                <?php echo e(__('After registration at :name, you will have Secret Key, Public Key & API Key', ['name' => 'Paymob'])); ?>

            </p>
        </li>
        <li>
            <p>
                <?php echo e(__('Enter Secret Key, Public Key & API Key into the box in right hand')); ?>

            </p>
        </li>
        <li>
            <p>
                <?php echo BaseHelper::clean('Please copy the bellow callback URL and paste it into paymob account.'); ?>

            </p>
            <p>
                <strong>Transaction Response Callback</strong>
                <code><?php echo e(route('payments.paymob.response')); ?></code>
            </p>

            <p>
            <strong>Transaction Processed Callback</strong>
            <code><?php echo e(route('payments.paymob.processed')); ?></code>
            </p>

        </li>
    </ol><?php /**PATH D:\laragon\www\martfury\storage\framework\views/d045688464dc1ce0d3261e7ec8e99314.blade.php ENDPATH**/ ?>