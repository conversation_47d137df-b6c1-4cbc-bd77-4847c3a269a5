<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Import Products from ERPNext</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="ti ti-info-circle"></i> Product Import Instructions</h5>
                        <ul class="mb-0">
                            <li>Select one or more Item Groups from ERPNext to import products from</li>
                            <li>Click "Preview Products" to see what products will be imported</li>
                            <li>Products with existing SKUs will be skipped to prevent duplicates</li>
                            <li>New product categories will be created automatically based on Item Groups</li>
                            <li>Click "Import Products" to start the import process</li>
                        </ul>
                    </div>

                    <form id="import-form" method="POST" action="<?php echo e(route('erpnext.product-import.import')); ?>">
                        <?php echo csrf_field(); ?>

                        <div class="form-group mb-3">
                            <label for="item_groups" class="form-label">Select Item Groups to Import</label>
                            <select name="item_groups[]" id="item_groups" class="form-control" multiple size="10">
                                <option value="">Loading Item Groups...</option>
                            </select>
                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple groups</small>
                        </div>

                        <div id="preview-section" style="display: none;" class="mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ti ti-eye"></i> Products Preview
                                        <span id="preview-count" class="badge bg-primary text-white ms-2"></span>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="preview-content"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="button" id="preview-btn" class="btn btn-info me-2" disabled>
                                <i class="ti ti-eye"></i> Preview Products
                            </button>
                            <button type="submit" id="import-btn" class="btn btn-success" disabled>
                                <i class="ti ti-download"></i> Import Products
                            </button>
                            <div id="loading" style="display: none;">
                                <div class="d-inline-block ms-3">
<div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Processing...</span>
                                </div>

                            </div>
                        </div>
                    </form>

                    <div id="results" style="display: none;" class="mt-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Import Results</h5>
                            </div>
                            <div class="card-body">
                                <div id="results-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer'); ?>
<script>
// Define routes for JavaScript
window.erpnextRoutes = {
    itemGroups: '<?php echo e(route("erpnext.product-import.item-groups")); ?>',
    preview: '<?php echo e(route("erpnext.product-import.preview")); ?>',
    import: '<?php echo e(route("erpnext.product-import.import")); ?>',
    status: '<?php echo e(route("erpnext.product-import.status")); ?>'
};
</script>
<script src="<?php echo e(asset('vendor/core/plugins/erpnext/js/product-import.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make(BaseHelper::getAdminMasterLayoutTemplate(), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\martfury\platform/plugins/erpnext/resources/views/product-import/index.blade.php ENDPATH**/ ?>