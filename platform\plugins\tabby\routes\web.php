<?php

use Bo<PERSON>ble\Tabby\Http\Controllers\TabbyController;
use Illuminate\Support\Facades\Route;

Route::middleware(['core'])
    ->prefix('payment/tabby')->name('payments.tabby.')
    ->group(function (): void {
        Route::get('callback/{token}', [TabbyController::class, 'callback'])->name('callback');
        
        Route::post('webhook', [TabbyController::class, 'webhook'])
            ->name('webhook');
    });
