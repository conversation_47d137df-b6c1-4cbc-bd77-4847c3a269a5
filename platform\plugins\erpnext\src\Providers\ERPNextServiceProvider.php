<?php

namespace Shaqi\ERPNext\Providers;

use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Facades\PanelSectionManager;
use Botble\Base\PanelSections\PanelSectionItem;
use Botble\Ecommerce\PanelSections\SettingEcommercePanelSection;

class ERPNextServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;


    public function boot(): void
    {
        $this
            ->setNamespace('plugins/erpnext')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'logging'])
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->loadMigrations();

            $this->app->register(HookServiceProvider::class);
            $this->app->register(EventServiceProvider::class);


            PanelSectionManager::default()->beforeRendering(function (): void {
                PanelSectionManager::registerItem(
                    SettingEcommercePanelSection::class,
                    fn () => PanelSectionItem::make('erpnext')
                        ->setTitle(trans('plugins/erpnext::erpnext.settings.title'))
                        ->withIcon('ti ti-mail-cog')
                        ->withPriority(140)
                        ->withDescription(trans('plugins/erpnext::erpnext.settings.description'))
                        ->withRoute('erpnext.settings')
                );
            });

            // Add ERPNext menu items to the admin dashboard
            DashboardMenu::default()->beforeRetrieving(function (): void {
                DashboardMenu::make()
                    ->registerItem([
                        'id' => 'cms-plugins-erpnext',
                        'priority' => 999,
                        'parent_id' => 'cms-plugins-ecommerce',
                        'name' => 'plugins/erpnext::erpnext.name',
                        'icon' => 'ti ti-cloud-upload',
                        'permissions' => ['erpnext.product-import'],
                    ])
                    ->registerItem([
                        'id' => 'cms-plugins-erpnext-product-import',
                        'priority' => 1,
                        'parent_id' => 'cms-plugins-erpnext',
                        'name' => 'plugins/erpnext::erpnext.product_import.title',
                        'icon' => 'ti ti-download',
                        'url' => fn () => route('erpnext.product-import.index'),
                        'permissions' => ['erpnext.product-import'],
                    ]);
            });

            // $this->app->booted(function () {

            // });

    }
}
