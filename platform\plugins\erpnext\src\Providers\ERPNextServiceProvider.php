<?php

namespace Shaqi\ERPNext\Providers;

use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Facades\PanelSectionManager;
use Botble\Base\PanelSections\PanelSectionItem;
use Botble\Ecommerce\PanelSections\SettingEcommercePanelSection;

class ERPNextServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;


    public function boot(): void
    {
        $this
            ->setNamespace('plugins/erpnext')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'logging'])
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->loadMigrations();

            $this->app->register(HookServiceProvider::class);
            $this->app->register(EventServiceProvider::class);


            PanelSectionManager::default()->beforeRendering(function (): void {
                PanelSectionManager::registerItem(
                    SettingEcommercePanelSection::class,
                    fn () => PanelSectionItem::make('erpnext')
                        ->setTitle(trans('plugins/erpnext::erpnext.settings.title'))
                        ->withIcon('ti ti-mail-cog')
                        ->withPriority(140)
                        ->withDescription(trans('plugins/erpnext::erpnext.settings.description'))
                        ->withRoute('erpnext.settings')
                );
            });

            // Add ERPNext menu items to the admin dashboard
            DashboardMenu::beforeRetrieving(function (): void {
                DashboardMenu::make()
                    ->registerItem([
                        'id' => 'cms-plugins-erpnext-import',
                        'priority' => 999,
                        'parent_id' => 'cms-plugins-ecommerce',
                        'name' => 'ERPNext Import',
                        'icon' => 'ti ti-cloud-download',
                        'url' => route('erpnext.product-import.index'),
                        'permissions' => ['products.index'],
                    ]);
            });

            // Add ERPNext import button to products table
            add_filter('table_header_actions', function ($actions, $table) {
                if (get_class($table) === 'Botble\Ecommerce\Tables\ProductTable') {
                    $actions['erpnext-import'] = [
                        'className' => 'action-item',
                        'text' => '<a href="' . route('erpnext.product-import.index') . '" class="btn btn-info btn-sm">
                                    <i class="ti ti-cloud-download"></i> Import from ERPNext
                                   </a>',
                    ];
                }
                return $actions;
            }, 10, 2);

            // $this->app->booted(function () {

            // });

    }
}
