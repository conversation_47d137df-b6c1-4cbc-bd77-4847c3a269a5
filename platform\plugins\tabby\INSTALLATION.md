# Tabby Pay-in-4 Plugin Installation Guide

This guide provides step-by-step instructions for installing and configuring the Tabby Pay-in-4 payment gateway plugin for Botble CMS.

## Prerequisites

Before installing the plugin, ensure you have:

- Botble CMS version 7.3.0 or higher
- PHP 8.1 or higher
- Composer installed
- A valid Tabby merchant account
- SSL certificate (required for production)

## Step 1: Download and Extract Plugin

1. Download the Tabby plugin package
2. Extract the files to your Botble installation:
   ```bash
   # Navigate to your Botble root directory
   cd /path/to/your/botble/installation
   
   # Extract plugin to plugins directory
   unzip tabby-plugin.zip -d platform/plugins/
   ```

## Step 2: Install Dependencies

Navigate to the plugin directory and install dependencies:

```bash
cd platform/plugins/tabby
composer install
```

## Step 3: Publish Plugin Assets

Publish the plugin assets (images, CSS, JS):

```bash
php artisan vendor:publish --tag=cms-public --force
```

## Step 4: Activate the Plugin

1. Log in to your Botble admin panel
2. Navigate to **Admin → Plugins**
3. Find "Tabby Pay-in-4 Payment Gateway" in the list
4. Click the **Activate** button

## Step 5: Configure Tabby Credentials

### Get Your Tabby Credentials

1. Register at [Tabby Merchant Portal](https://merchant.tabby.ai/)
2. Complete the merchant verification process
3. Access your dashboard to retrieve:
   - Public Key
   - Secret Key
   - Merchant Code

### Configure Plugin Settings

1. In Botble admin, go to **Admin → Plugins → Payment Methods**
2. Scroll to the "Tabby Pay-in-4" section
3. Fill in your credentials:
   - **Status**: Enable
   - **Public Key**: Your Tabby public API key
   - **Secret Key**: Your Tabby secret API key
   - **Merchant Code**: Your merchant code from Tabby
   - **Environment**: Select "Sandbox" for testing, "Live" for production
4. Click **Save Settings**

## Step 6: Configure Webhooks

### Register Webhook URL

1. Log in to your Tabby Merchant Dashboard
2. Navigate to **Settings → Webhooks**
3. Add a new webhook with URL:
   ```
   https://yourdomain.com/payment/tabby/webhook
   ```
4. Select all event types or at minimum:
   - Payment Authorized
   - Payment Closed
   - Payment Rejected
   - Payment Expired

### Test Webhook (Optional)

Use a tool like [webhook.site](https://webhook.site) to test webhook delivery:

1. Create a temporary webhook URL at webhook.site
2. Add it to your Tabby dashboard
3. Process a test payment
4. Verify webhook events are received

## Step 7: Test the Integration

### Sandbox Testing

1. Ensure "Sandbox" environment is selected
2. Use Tabby's test credentials if provided
3. Create a test order:
   - Add products to cart
   - Proceed to checkout
   - Select "Tabby Pay-in-4" payment method
   - Complete the payment flow

### Test Scenarios

Test these scenarios to ensure proper integration:

1. **Successful Payment**
   - Complete payment on Tabby's page
   - Verify order is marked as paid
   - Check webhook events are processed

2. **Payment Rejection**
   - Test with amounts outside limits
   - Verify rejection messages are shown
   - Ensure graceful error handling

3. **Payment Cancellation**
   - Cancel payment on Tabby's page
   - Verify order remains unpaid
   - Check proper redirect handling

4. **Eligibility Check**
   - Test with different amounts
   - Verify pre-scoring works correctly
   - Check payment method visibility

## Step 8: Go Live

### Switch to Production

1. Obtain live API credentials from Tabby
2. Update plugin settings:
   - Change environment to "Live"
   - Update API credentials
   - Save settings

### Production Checklist

- [ ] SSL certificate is installed and working
- [ ] Live API credentials are configured
- [ ] Webhook URL is registered with live credentials
- [ ] Test a small live transaction
- [ ] Monitor logs for any errors
- [ ] Verify order processing workflow

## Troubleshooting

### Common Issues

**Plugin Not Showing in Admin**
- Verify files are in correct directory: `platform/plugins/tabby/`
- Check file permissions
- Clear cache: `php artisan cache:clear`

**Payment Method Not Visible**
- Check plugin is activated
- Verify API credentials are correct
- Ensure currency is supported (AED, SAR, KWD, BHD, QAR, EGP)
- Check eligibility requirements

**Webhook Not Working**
- Verify webhook URL is accessible
- Check SSL certificate is valid
- Review server logs for errors
- Test webhook URL manually

**Payment Failures**
- Check API credentials
- Verify environment setting (sandbox vs live)
- Review Tabby merchant dashboard
- Check application logs

### Debug Mode

Enable debug logging by adding to `.env`:
```
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed information.

### Getting Help

- **Plugin Issues**: Check GitHub repository or contact developer
- **Tabby API Issues**: Contact <EMAIL>
- **Botble Issues**: Visit https://botble.com/contact

## Security Considerations

- Always use HTTPS in production
- Keep API credentials secure
- Regularly update the plugin
- Monitor transaction logs
- Implement proper error handling

## Performance Optimization

- Enable caching for better performance
- Monitor API response times
- Implement proper logging
- Use queue workers for webhook processing

## Maintenance

- Regularly check for plugin updates
- Monitor Tabby API status
- Review transaction logs
- Keep dependencies updated
- Backup configuration settings

## Support

For technical support:
- Email: <EMAIL>
- Documentation: https://docs.tabby.ai/
- Merchant Portal: https://merchant.tabby.ai/

## Next Steps

After successful installation:
1. Configure promotional messaging (optional)
2. Set up monitoring and alerts
3. Train staff on the new payment method
4. Update customer documentation
5. Monitor initial transactions closely
