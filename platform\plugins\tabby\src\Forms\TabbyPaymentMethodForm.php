<?php

namespace Bo<PERSON><PERSON>\Tabby\Forms;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Forms\PaymentMethodForm;

class TabbyPaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(TABBY_PAYMENT_METHOD_NAME)
            ->paymentName('Tabby Pay-in-4')
            ->paymentDescription(__('Customer can buy products and pay in 4 installments with Tabby. No interest, no fees.'))
            ->paymentLogo(url('vendor/core/plugins/tabby/images/tabby.svg'))
            ->paymentUrl('https://tabby.ai')
            ->paymentInstructions(view('plugins/tabby::instructions')->render())
            ->add(
                sprintf('payment_%s_public_key', TABBY_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Public Key'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('public_key', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby public API key'))
            )
            ->add(
                sprintf('payment_%s_secret_key', TABBY_PAYMENT_METHOD_NAME),
                'password',
                TextFieldOption::make()
                    ->label(__('Secret Key'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('secret_key', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby secret API key'))
            )
            ->add(
                sprintf('payment_%s_merchant_code', TABBY_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Merchant Code'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('merchant_code', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby merchant code'))
            )
            ->add(
                'payment_' . TABBY_PAYMENT_METHOD_NAME . '_environment',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('Environment'))
                    ->choices([
                        'sandbox' => __('Sandbox (Test)'),
                        'live' => __('Live (Production)'),
                    ])
                    ->selected(get_payment_setting('environment', TABBY_PAYMENT_METHOD_NAME, 'sandbox'))
                    ->helperText(__('Select the environment for Tabby payments'))
            );
    }
}
