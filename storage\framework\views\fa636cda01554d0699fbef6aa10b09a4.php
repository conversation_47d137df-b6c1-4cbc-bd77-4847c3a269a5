<?php $__env->startSection('content'); ?>

    <?php if (isset($component)) { $__componentOriginal26a807ae0e75175dceaac1113c75830b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal26a807ae0e75175dceaac1113c75830b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'a74ad8dfacd4f985eb3977517615ce25::panel-section','data' => ['id' => 'system']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::panel-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'system']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal26a807ae0e75175dceaac1113c75830b)): ?>
<?php $attributes = $__attributesOriginal26a807ae0e75175dceaac1113c75830b; ?>
<?php unset($__attributesOriginal26a807ae0e75175dceaac1113c75830b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal26a807ae0e75175dceaac1113c75830b)): ?>
<?php $component = $__componentOriginal26a807ae0e75175dceaac1113c75830b; ?>
<?php unset($__componentOriginal26a807ae0e75175dceaac1113c75830b); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make(BaseHelper::getAdminMasterLayoutTemplate(), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\martfury\platform/core/base/resources/views/system/index.blade.php ENDPATH**/ ?>