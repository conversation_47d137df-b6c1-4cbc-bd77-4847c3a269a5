[2025-08-06 16:56:51] local.INFO: Fetching Item Groups for import form  
[2025-08-06 16:56:51] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 16:56:52] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 16:56:52] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 16:56:53] local.INFO: Fetching Item Groups for import form  
[2025-08-06 16:56:53] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 16:56:54] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 16:56:54] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 16:57:08] local.INFO: Starting product import process {"selected_groups":["Demo Item Group"],"user_id":1} 
[2025-08-06 16:57:08] local.INFO: Starting product import {"selected_groups":["Demo Item Group"]} 
[2025-08-06 16:57:08] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 16:57:08] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 16:57:08] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":77,"sku":"KT-137","name":"Macbook Pro 2015 13 inch"} 
[2025-08-06 16:57:09] local.INFO: Created new product category {"category_id":38,"name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":77,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":77,"item_code":"KT-137","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (77, KT-137, Macbook Pro 2015 13 inch, Demo Item Group, 0, KT-137, imported, {\"name\":\"KT-137\",\"item_name\":\"Macbook Pro 2015 13 inch\",\"item_code\":\"KT-137\",\"item_group\":\"Demo Item Group\",\"description\":\"KT-137\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":null,\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":78,"sku":"SKU001","name":"T-shirt"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":78,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":78,"item_code":"SKU001","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (78, SKU001, T-shirt, Demo Item Group, 0, T-shirt, imported, {\"name\":\"SKU001\",\"item_name\":\"T-shirt\",\"item_code\":\"SKU001\",\"item_group\":\"Demo Item Group\",\"description\":\"T-shirt\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/1484808\\/pexels-photo-1484808.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":79,"sku":"SKU002","name":"Laptop"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":79,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":79,"item_code":"SKU002","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (79, SKU002, Laptop, Demo Item Group, 0, Laptop, imported, {\"name\":\"SKU002\",\"item_name\":\"Laptop\",\"item_code\":\"SKU002\",\"item_group\":\"Demo Item Group\",\"description\":\"Laptop\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/3999538\\/pexels-photo-3999538.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":80,"sku":"SKU003","name":"Book"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":80,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":80,"item_code":"SKU003","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (80, SKU003, Book, Demo Item Group, 0, Book, imported, {\"name\":\"SKU003\",\"item_name\":\"Book\",\"item_code\":\"SKU003\",\"item_group\":\"Demo Item Group\",\"description\":\"Book\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/2422178\\/pexels-photo-2422178.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":81,"sku":"SKU004","name":"Smartphone"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":81,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":81,"item_code":"SKU004","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (81, SKU004, Smartphone, Demo Item Group, 0, Smartphone, imported, {\"name\":\"SKU004\",\"item_name\":\"Smartphone\",\"item_code\":\"SKU004\",\"item_group\":\"Demo Item Group\",\"description\":\"Smartphone\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/1647976\\/pexels-photo-1647976.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":82,"sku":"SKU005","name":"Sneakers"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":82,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":82,"item_code":"SKU005","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (82, SKU005, Sneakers, Demo Item Group, 0, Sneakers, imported, {\"name\":\"SKU005\",\"item_name\":\"Sneakers\",\"item_code\":\"SKU005\",\"item_group\":\"Demo Item Group\",\"description\":\"Sneakers\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/1598505\\/pexels-photo-1598505.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":83,"sku":"SKU006","name":"Coffee Mug"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":83,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":83,"item_code":"SKU006","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (83, SKU006, Coffee Mug, Demo Item Group, 0, Coffee Mug, imported, {\"name\":\"SKU006\",\"item_name\":\"Coffee Mug\",\"item_code\":\"SKU006\",\"item_group\":\"Demo Item Group\",\"description\":\"Coffee Mug\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/585753\\/pexels-photo-585753.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":84,"sku":"SKU007","name":"Television"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":84,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":84,"item_code":"SKU007","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (84, SKU007, Television, Demo Item Group, 0, Television, imported, {\"name\":\"SKU007\",\"item_name\":\"Television\",\"item_code\":\"SKU007\",\"item_group\":\"Demo Item Group\",\"description\":\"Television\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/8059376\\/pexels-photo-8059376.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":85,"sku":"SKU008","name":"Backpack"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":85,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":85,"item_code":"SKU008","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (85, SKU008, Backpack, Demo Item Group, 0, Backpack, imported, {\"name\":\"SKU008\",\"item_name\":\"Backpack\",\"item_code\":\"SKU008\",\"item_group\":\"Demo Item Group\",\"description\":\"Backpack\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/3731256\\/pexels-photo-3731256.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":86,"sku":"SKU009","name":"Headphones"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":86,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":86,"item_code":"SKU009","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (86, SKU009, Headphones, Demo Item Group, 0, Headphones, imported, {\"name\":\"SKU009\",\"item_name\":\"Headphones\",\"item_code\":\"SKU009\",\"item_group\":\"Demo Item Group\",\"description\":\"Headphones\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/3587478\\/pexels-photo-3587478.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Created product {"product_id":87,"sku":"SKU010","name":"Camera"} 
[2025-08-06 16:57:09] local.INFO: Assigned product to category {"product_id":87,"category_id":38,"category_name":"Demo Item Group"} 
[2025-08-06 16:57:09] local.WARNING: Failed to track import {"product_id":87,"item_code":"SKU010","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'martfury.erpnext_product_imports' doesn't exist (Connection: mysql, SQL: insert into `erpnext_product_imports` (`product_id`, `erpnext_item_code`, `erpnext_item_name`, `erpnext_item_group`, `erpnext_standard_rate`, `erpnext_description`, `import_status`, `import_data`, `imported_at`, `imported_by`, `created_at`, `updated_at`) values (87, SKU010, Camera, Demo Item Group, 0, Camera, imported, {\"name\":\"SKU010\",\"item_name\":\"Camera\",\"item_code\":\"SKU010\",\"item_group\":\"Demo Item Group\",\"description\":\"Camera\",\"standard_rate\":0,\"stock_uom\":\"Nos\",\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/51383\\/photo-camera-subject-photographer-51383.jpeg\",\"brand\":null,\"disabled\":0}, 2025-08-06 16:57:09, 1, 2025-08-06 16:57:09, 2025-08-06 16:57:09))"} 
[2025-08-06 16:57:09] local.INFO: Product import completed {"total":11,"imported":11,"skipped":0,"errors":0,"details":[{"type":"imported","sku":"KT-137","name":"Macbook Pro 2015 13 inch","id":77},{"type":"imported","sku":"SKU001","name":"T-shirt","id":78},{"type":"imported","sku":"SKU002","name":"Laptop","id":79},{"type":"imported","sku":"SKU003","name":"Book","id":80},{"type":"imported","sku":"SKU004","name":"Smartphone","id":81},{"type":"imported","sku":"SKU005","name":"Sneakers","id":82},{"type":"imported","sku":"SKU006","name":"Coffee Mug","id":83},{"type":"imported","sku":"SKU007","name":"Television","id":84},{"type":"imported","sku":"SKU008","name":"Backpack","id":85},{"type":"imported","sku":"SKU009","name":"Headphones","id":86},{"type":"imported","sku":"SKU010","name":"Camera","id":87}]} 
[2025-08-06 16:57:18] local.INFO: Previewing products for selected groups {"groups":["Demo Item Group"]} 
[2025-08-06 16:57:18] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 16:57:19] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 16:57:19] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
[2025-08-06 16:57:52] local.INFO: Fetching Item Groups for import form  
[2025-08-06 16:57:52] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 16:57:52] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 16:57:52] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 16:57:53] local.INFO: Fetching Item Groups for import form  
[2025-08-06 16:57:53] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 16:57:54] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 16:57:54] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 16:59:11] local.INFO: Previewing products for selected groups {"groups":["Demo Item Group"]} 
[2025-08-06 16:59:11] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 16:59:12] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 16:59:12] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
[2025-08-06 16:59:19] local.INFO: Starting product import process {"selected_groups":["Demo Item Group"],"user_id":1} 
[2025-08-06 16:59:19] local.INFO: Starting product import {"selected_groups":["Demo Item Group"]} 
[2025-08-06 16:59:19] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 16:59:20] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 16:59:20] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
[2025-08-06 16:59:20] local.INFO: Product import completed {"total":11,"imported":0,"skipped":11,"errors":0,"details":[{"type":"skipped","sku":"KT-137","name":"Macbook Pro 2015 13 inch","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU001","name":"T-shirt","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU002","name":"Laptop","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU003","name":"Book","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU004","name":"Smartphone","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU005","name":"Sneakers","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU006","name":"Coffee Mug","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU007","name":"Television","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU008","name":"Backpack","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU009","name":"Headphones","reason":"Product with this SKU already exists"},{"type":"skipped","sku":"SKU010","name":"Camera","reason":"Product with this SKU already exists"}]} 
[2025-08-06 17:01:41] local.INFO: Fetching Item Groups for import form  
[2025-08-06 17:01:41] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 17:01:42] local.INFO: Fetching Item Groups for import form  
[2025-08-06 17:01:42] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","data":[],"headers":[]} 
[2025-08-06 17:01:42] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 17:01:42] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 17:01:43] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item Group","status_code":200,"response":{"data":[{"name":"All Item Groups","item_group_name":"All Item Groups","parent_item_group":"","is_group":1},{"name":"Consumable","item_group_name":"Consumable","parent_item_group":"All Item Groups","is_group":0},{"name":"Demo Item Group","item_group_name":"Demo Item Group","parent_item_group":"All Item Groups","is_group":0},{"name":"Products","item_group_name":"Products","parent_item_group":"All Item Groups","is_group":0},{"name":"Raw Material","item_group_name":"Raw Material","parent_item_group":"All Item Groups","is_group":0},{"name":"Services","item_group_name":"Services","parent_item_group":"All Item Groups","is_group":0},{"name":"Sub Assemblies","item_group_name":"Sub Assemblies","parent_item_group":"All Item Groups","is_group":0}]}} 
[2025-08-06 17:01:43] local.INFO: Successfully fetched Item Groups {"count":7} 
[2025-08-06 17:05:45] local.INFO: Previewing products for selected groups {"groups":["Demo Item Group"]} 
[2025-08-06 17:05:45] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 17:05:46] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 17:05:46] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
[2025-08-06 17:05:59] local.INFO: Previewing products for selected groups {"groups":["Products"]} 
[2025-08-06 17:05:59] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Products\"]]"},"headers":[]} 
[2025-08-06 17:06:00] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[]}} 
[2025-08-06 17:06:00] local.INFO: Successfully fetched products for Item Group {"item_group":"Products","count":0} 
[2025-08-06 17:06:18] local.INFO: Previewing products for selected groups {"groups":["Raw Material"]} 
[2025-08-06 17:06:18] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Raw Material\"]]"},"headers":[]} 
[2025-08-06 17:06:19] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[]}} 
[2025-08-06 17:06:19] local.INFO: Successfully fetched products for Item Group {"item_group":"Raw Material","count":0} 
[2025-08-06 17:06:25] local.INFO: Previewing products for selected groups {"groups":["All Item Groups"]} 
[2025-08-06 17:06:25] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"All Item Groups\"]]"},"headers":[]} 
[2025-08-06 17:06:25] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[]}} 
[2025-08-06 17:06:25] local.INFO: Successfully fetched products for Item Group {"item_group":"All Item Groups","count":0} 
[2025-08-06 17:06:30] local.INFO: Previewing products for selected groups {"groups":["Demo Item Group"]} 
[2025-08-06 17:06:30] local.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","data":{"filters":"[[\"item_group\",\"=\",\"Demo Item Group\"]]"},"headers":[]} 
[2025-08-06 17:06:30] local.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Item","status_code":200,"response":{"data":[{"name":"KT-137","item_name":"Macbook Pro 2015 13 inch","item_code":"KT-137","item_group":"Demo Item Group","description":"KT-137","standard_rate":0.0,"stock_uom":"Nos","image":null,"brand":null,"disabled":0},{"name":"SKU001","item_name":"T-shirt","item_code":"SKU001","item_group":"Demo Item Group","description":"T-shirt","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1484808/pexels-photo-1484808.jpeg","brand":null,"disabled":0},{"name":"SKU002","item_name":"Laptop","item_code":"SKU002","item_group":"Demo Item Group","description":"Laptop","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3999538/pexels-photo-3999538.jpeg","brand":null,"disabled":0},{"name":"SKU003","item_name":"Book","item_code":"SKU003","item_group":"Demo Item Group","description":"Book","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/2422178/pexels-photo-2422178.jpeg","brand":null,"disabled":0},{"name":"SKU004","item_name":"Smartphone","item_code":"SKU004","item_group":"Demo Item Group","description":"Smartphone","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg","brand":null,"disabled":0},{"name":"SKU005","item_name":"Sneakers","item_code":"SKU005","item_group":"Demo Item Group","description":"Sneakers","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg","brand":null,"disabled":0},{"name":"SKU006","item_name":"Coffee Mug","item_code":"SKU006","item_group":"Demo Item Group","description":"Coffee Mug","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/585753/pexels-photo-585753.jpeg","brand":null,"disabled":0},{"name":"SKU007","item_name":"Television","item_code":"SKU007","item_group":"Demo Item Group","description":"Television","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/8059376/pexels-photo-8059376.jpeg","brand":null,"disabled":0},{"name":"SKU008","item_name":"Backpack","item_code":"SKU008","item_group":"Demo Item Group","description":"Backpack","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3731256/pexels-photo-3731256.jpeg","brand":null,"disabled":0},{"name":"SKU009","item_name":"Headphones","item_code":"SKU009","item_group":"Demo Item Group","description":"Headphones","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/3587478/pexels-photo-3587478.jpeg","brand":null,"disabled":0},{"name":"SKU010","item_name":"Camera","item_code":"SKU010","item_group":"Demo Item Group","description":"Camera","standard_rate":0.0,"stock_uom":"Nos","image":"https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg","brand":null,"disabled":0}]}} 
[2025-08-06 17:06:30] local.INFO: Successfully fetched products for Item Group {"item_group":"Demo Item Group","count":11} 
