@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            {!! $form->renderForm() !!}
        </div>
    </div>
@endsection

@push('footer')
<script>
// Define routes for JavaScript
window.erpnextRoutes = {
    itemGroups: '{{ route("erpnext.product-import.item-groups") }}',
    preview: '{{ route("erpnext.product-import.preview") }}',
    import: '{{ route("erpnext.product-import.import") }}',
    status: '{{ route("erpnext.product-import.status") }}'
};
</script>
<script>
$(document).ready(function() {
    let selectedItemGroups = [];
    let previewData = [];

    // Load Item Groups on page load
    loadItemGroups();

    // Preview button click handler
    $('#preview-btn').on('click', function() {
        previewProducts();
    });

    // Form submit handler
    $('#main-form').on('submit', function(e) {
        e.preventDefault();
        importProducts();
    });

    // Item groups selection change handler
    $(document).on('change', '#item_groups_select', function() {
        selectedItemGroups = $(this).val() || [];
        updateButtonStates();
        hidePreview();
    });

    function loadItemGroups() {
        $('#item-groups-loading').show();
        $('#item-groups-error').hide();

        $.ajax({
            url: '{{ route("erpnext.product-import.item-groups") }}',
            type: 'GET',
            success: function(response) {
                if (response.error) {
                    showItemGroupsError(response.message);
                    return;
                }

                populateItemGroupsSelect(response.data);
                $('#item-groups-loading').hide();
            },
            error: function(xhr) {
                let message = 'Failed to load Item Groups';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showItemGroupsError(message);
            }
        });
    }

    function populateItemGroupsSelect(itemGroups) {
        const select = $('#item_groups_select');
        select.empty();

        itemGroups.forEach(function(group) {
            select.append(new Option(group.label, group.value, false, false));
        });

        select.attr('data-placeholder', 'Select Item Groups to import from...');
        select.trigger('change');
        updateButtonStates();
    }

    function showItemGroupsError(message) {
        $('#item-groups-loading').hide();
        $('#item-groups-error-message').text(message);
        $('#item-groups-error').show();
    }

    function previewProducts() {
        if (selectedItemGroups.length === 0) {
            Botble.showError('Please select at least one Item Group');
            return;
        }

        $('#preview-loading').show();
        $('#preview-content').empty();
        $('#preview-section').show();

        $.ajax({
            url: '{{ route("erpnext.product-import.preview") }}',
            type: 'POST',
            data: {
                item_groups: selectedItemGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#preview-loading').hide();

                if (response.error) {
                    Botble.showError(response.message);
                    return;
                }

                previewData = response.data;
                displayPreview(response.data, response.total);
                updateButtonStates();
            },
            error: function(xhr) {
                $('#preview-loading').hide();
                let message = 'Failed to preview products';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Botble.showError(message);
            }
        });
    }

    function displayPreview(products, total) {
        $('#preview-count').text(total + ' products');

        if (products.length === 0) {
            $('#preview-content').html('<div class="alert alert-warning">No products found in selected Item Groups.</div>');
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>SKU</th><th>Name</th><th>Item Group</th><th>Price</th><th>Status</th></tr></thead><tbody>';

        products.forEach(function(product) {
            const status = product.disabled ? '<span class="badge bg-danger">Disabled</span>' : '<span class="badge bg-success">Active</span>';
            const price = product.standard_rate ? '$' + parseFloat(product.standard_rate).toFixed(2) : 'N/A';

            html += `<tr>
                <td><code>${product.item_code}</code></td>
                <td>${product.item_name}</td>
                <td>${product.item_group}</td>
                <td>${price}</td>
                <td>${status}</td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        $('#preview-content').html(html);
    }

    function importProducts() {
        if (selectedItemGroups.length === 0) {
            Botble.showError('Please select at least one Item Group');
            return;
        }

        $('#import-loading').show();
        $('#import-btn').prop('disabled', true);

        $.ajax({
            url: '{{ route("erpnext.product-import.import") }}',
            type: 'POST',
            data: {
                item_groups: selectedItemGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                if (response.error) {
                    Botble.showError(response.message);
                    return;
                }

                displayImportResults(response.data, response.additional_data);

                if (response.additional_data.type === 'success') {
                    Botble.showSuccess(response.message);
                } else {
                    Botble.showWarning(response.message);
                }
            },
            error: function(xhr) {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                let message = 'Import failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Botble.showError(message);
            }
        });
    }

    function displayImportResults(results, additionalData) {
        const stats = `
            <div class="row">
                <div class="col-md-3"><strong>Total:</strong> ${results.total}</div>
                <div class="col-md-3"><strong>Imported:</strong> <span class="text-success">${results.imported}</span></div>
                <div class="col-md-3"><strong>Skipped:</strong> <span class="text-warning">${results.skipped}</span></div>
                <div class="col-md-3"><strong>Errors:</strong> <span class="text-danger">${results.errors}</span></div>
            </div>
        `;

        $('#import-stats').html(stats);

        if (results.details && results.details.length > 0) {
            let detailsHtml = '<div class="list-group">';
            results.details.forEach(function(detail) {
                let badgeClass = 'bg-secondary';
                let icon = 'ti ti-info-circle';

                if (detail.type === 'imported') {
                    badgeClass = 'bg-success';
                    icon = 'ti ti-check';
                } else if (detail.type === 'skipped') {
                    badgeClass = 'bg-warning';
                    icon = 'ti ti-alert-triangle';
                } else if (detail.type === 'error') {
                    badgeClass = 'bg-danger';
                    icon = 'ti ti-x';
                }

                detailsHtml += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="${icon}"></i> ${detail.name || 'Unknown'}
                                    <span class="badge ${badgeClass} ms-2">${detail.type}</span>
                                </h6>
                                <p class="mb-1"><small><strong>SKU:</strong> ${detail.sku || 'N/A'}</small></p>
                                ${detail.reason ? `<small class="text-muted">${detail.reason}</small>` : ''}
                                ${detail.error ? `<small class="text-danger">${detail.error}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            detailsHtml += '</div>';
            $('#import-details-content').html(detailsHtml);
        }

        $('#import-results').show();
    }

    function updateButtonStates() {
        const hasSelection = selectedItemGroups.length > 0;
        $('#preview-btn').prop('disabled', !hasSelection);
        $('#import-btn').prop('disabled', !hasSelection || previewData.length === 0);
    }

    function hidePreview() {
        $('#preview-section').hide();
        previewData = [];
        updateButtonStates();
    }
});
</script>
@endpush
