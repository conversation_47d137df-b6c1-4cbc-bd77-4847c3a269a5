@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Import Products from ERPNext</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="ti ti-info-circle"></i> Product Import Instructions</h5>
                        <ul class="mb-0">
                            <li>Select one or more Item Groups from ERPNext to import products from</li>
                            <li>Click "Preview Products" to see what products will be imported</li>
                            <li>Products with existing SKUs will be skipped to prevent duplicates</li>
                            <li>New product categories will be created automatically based on Item Groups</li>
                            <li>Click "Import Products" to start the import process</li>
                        </ul>
                    </div>

                    <form id="import-form" method="POST" action="{{ route('erpnext.product-import.import') }}">
                        @csrf

                        <div class="form-group mb-3">
                            <label for="item_groups" class="form-label">Select Item Groups to Import</label>
                            <select name="item_groups[]" id="item_groups" class="form-control" multiple size="10">
                                <option value="">Loading Item Groups...</option>
                            </select>
                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple groups</small>
                        </div>

                        <div id="preview-section" style="display: none;" class="mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ti ti-eye"></i> Products Preview
                                        <span id="preview-count" class="badge bg-primary text-white ms-2"></span>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="preview-content"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="button" id="preview-btn" class="btn btn-info me-2" disabled>
                                <i class="ti ti-eye"></i> Preview Products
                            </button>
                            <button type="submit" id="import-btn" class="btn btn-success" disabled>
                                <i class="ti ti-download"></i> Import Products
                            </button>
                            <div id="loading" class="ms-3" style="display: none;">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Processing...</span>
                            </div>
                        </div>
                    </form>

                    <div id="results" style="display: none;" class="mt-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Import Results</h5>
                            </div>
                            <div class="card-body">
                                <div id="results-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    let selectedGroups = [];

    // Load Item Groups on page load
    loadItemGroups();

    // Item groups selection change
    $('#item_groups').on('change', function() {
        selectedGroups = $(this).val() || [];
        updateButtons();
        hidePreview();
    });

    // Preview button click
    $('#preview-btn').on('click', function() {
        previewProducts();
    });

    // Form submit
    $('#import-form').on('submit', function(e) {
        e.preventDefault();
        importProducts();
    });

    function loadItemGroups() {
        $('#loading').show();

        $.ajax({
            url: '{{ route("erpnext.product-import.item-groups") }}',
            type: 'GET',
            success: function(response) {
                $('#loading').hide();

                if (response.error) {
                    alert('Error: ' + response.message);
                    return;
                }

                populateItemGroups(response.data);
            },
            error: function() {
                $('#loading').hide();
                alert('Failed to load Item Groups');
            }
        });
    }

    function populateItemGroups(groups) {
        const select = $('#item_groups');
        select.empty();

        groups.forEach(function(group) {
            select.append(new Option(group.label, group.value));
        });

        updateButtons();
    }

    function previewProducts() {
        if (selectedGroups.length === 0) {
            alert('Please select at least one Item Group');
            return;
        }

        $('#loading').show();

        $.ajax({
            url: '{{ route("erpnext.product-import.preview") }}',
            type: 'POST',
            data: {
                item_groups: selectedGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loading').hide();

                if (response.error) {
                    alert('Error: ' + response.message);
                    return;
                }

                displayPreview(response.data, response.total);
            },
            error: function() {
                $('#loading').hide();
                alert('Failed to preview products');
            }
        });
    }

    function displayPreview(products, total) {
        $('#preview-count').text(total + ' products');

        if (products.length === 0) {
            $('#preview-content').html('<div class="alert alert-warning">No products found.</div>');
        } else {
            let html = '<div class="table-responsive"><table class="table table-striped">';
            html += '<thead><tr><th>SKU</th><th>Name</th><th>Group</th><th>Price</th></tr></thead><tbody>';

            products.forEach(function(product) {
                const price = product.standard_rate ? '$' + parseFloat(product.standard_rate).toFixed(2) : 'N/A';
                html += '<tr>';
                html += '<td><code>' + product.item_code + '</code></td>';
                html += '<td>' + product.item_name + '</td>';
                html += '<td>' + product.item_group + '</td>';
                html += '<td>' + price + '</td>';
                html += '</tr>';
            });

            html += '</tbody></table></div>';
            $('#preview-content').html(html);
        }

        $('#preview-section').show();
        updateButtons();
    }

    function importProducts() {
        if (selectedGroups.length === 0) {
            alert('Please select at least one Item Group');
            return;
        }

        $('#loading').show();
        $('#import-btn').prop('disabled', true);

        $.ajax({
            url: '{{ route("erpnext.product-import.import") }}',
            type: 'POST',
            data: {
                item_groups: selectedGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loading').hide();
                $('#import-btn').prop('disabled', false);

                if (response.error) {
                    alert('Error: ' + response.message);
                    return;
                }

                displayResults(response.data);
                alert('Import completed! Check results below.');
            },
            error: function() {
                $('#loading').hide();
                $('#import-btn').prop('disabled', false);
                alert('Import failed');
            }
        });
    }

    function displayResults(results) {
        let html = '<div class="row">';
        html += '<div class="col-md-3"><strong>Total:</strong> ' + results.total + '</div>';
        html += '<div class="col-md-3"><strong>Imported:</strong> <span class="text-success">' + results.imported + '</span></div>';
        html += '<div class="col-md-3"><strong>Skipped:</strong> <span class="text-warning">' + results.skipped + '</span></div>';
        html += '<div class="col-md-3"><strong>Errors:</strong> <span class="text-danger">' + results.errors + '</span></div>';
        html += '</div>';

        $('#results-content').html(html);
        $('#results').show();
    }

    function updateButtons() {
        const hasSelection = selectedGroups.length > 0;
        $('#preview-btn').prop('disabled', !hasSelection);
        $('#import-btn').prop('disabled', !hasSelection);
    }

    function hidePreview() {
        $('#preview-section').hide();
        $('#results').hide();
    }
});
$(document).ready(function() {
    let selectedItemGroups = [];
    let previewData = [];

    // Load Item Groups on page load
    loadItemGroups();

    // Preview button click handler
    $('#preview-btn').on('click', function() {
        previewProducts();
    });

    // Form submit handler
    $('#main-form').on('submit', function(e) {
        e.preventDefault();
        importProducts();
    });

    // Item groups selection change handler
    $(document).on('change', '#item_groups_select', function() {
        selectedItemGroups = $(this).val() || [];
        updateButtonStates();
        hidePreview();
    });

    function loadItemGroups() {
        $('#item-groups-loading').show();
        $('#item-groups-error').hide();

        $.ajax({
            url: '{{ route("erpnext.product-import.item-groups") }}',
            type: 'GET',
            success: function(response) {
                if (response.error) {
                    showItemGroupsError(response.message);
                    return;
                }

                populateItemGroupsSelect(response.data);
                $('#item-groups-loading').hide();
            },
            error: function(xhr) {
                let message = 'Failed to load Item Groups';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showItemGroupsError(message);
            }
        });
    }

    function populateItemGroupsSelect(itemGroups) {
        const select = $('#item_groups_select');
        select.empty();

        itemGroups.forEach(function(group) {
            select.append(new Option(group.label, group.value, false, false));
        });

        select.attr('data-placeholder', 'Select Item Groups to import from...');
        select.trigger('change');
        updateButtonStates();
    }

    function showItemGroupsError(message) {
        $('#item-groups-loading').hide();
        $('#item-groups-error-message').text(message);
        $('#item-groups-error').show();
    }

    function previewProducts() {
        if (selectedItemGroups.length === 0) {
            Botble.showError('Please select at least one Item Group');
            return;
        }

        $('#preview-loading').show();
        $('#preview-content').empty();
        $('#preview-section').show();

        $.ajax({
            url: '{{ route("erpnext.product-import.preview") }}',
            type: 'POST',
            data: {
                item_groups: selectedItemGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#preview-loading').hide();

                if (response.error) {
                    Botble.showError(response.message);
                    return;
                }

                previewData = response.data;
                displayPreview(response.data, response.total);
                updateButtonStates();
            },
            error: function(xhr) {
                $('#preview-loading').hide();
                let message = 'Failed to preview products';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Botble.showError(message);
            }
        });
    }

    function displayPreview(products, total) {
        $('#preview-count').text(total + ' products');

        if (products.length === 0) {
            $('#preview-content').html('<div class="alert alert-warning">No products found in selected Item Groups.</div>');
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>SKU</th><th>Name</th><th>Item Group</th><th>Price</th><th>Status</th></tr></thead><tbody>';

        products.forEach(function(product) {
            const status = product.disabled ? '<span class="badge bg-danger">Disabled</span>' : '<span class="badge bg-success">Active</span>';
            const price = product.standard_rate ? '$' + parseFloat(product.standard_rate).toFixed(2) : 'N/A';

            html += `<tr>
                <td><code>${product.item_code}</code></td>
                <td>${product.item_name}</td>
                <td>${product.item_group}</td>
                <td>${price}</td>
                <td>${status}</td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        $('#preview-content').html(html);
    }

    function importProducts() {
        if (selectedItemGroups.length === 0) {
            Botble.showError('Please select at least one Item Group');
            return;
        }

        $('#import-loading').show();
        $('#import-btn').prop('disabled', true);

        $.ajax({
            url: '{{ route("erpnext.product-import.import") }}',
            type: 'POST',
            data: {
                item_groups: selectedItemGroups,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                if (response.error) {
                    Botble.showError(response.message);
                    return;
                }

                displayImportResults(response.data, response.additional_data);

                if (response.additional_data.type === 'success') {
                    Botble.showSuccess(response.message);
                } else {
                    Botble.showWarning(response.message);
                }
            },
            error: function(xhr) {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                let message = 'Import failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Botble.showError(message);
            }
        });
    }

    function displayImportResults(results, additionalData) {
        const stats = `
            <div class="row">
                <div class="col-md-3"><strong>Total:</strong> ${results.total}</div>
                <div class="col-md-3"><strong>Imported:</strong> <span class="text-success">${results.imported}</span></div>
                <div class="col-md-3"><strong>Skipped:</strong> <span class="text-warning">${results.skipped}</span></div>
                <div class="col-md-3"><strong>Errors:</strong> <span class="text-danger">${results.errors}</span></div>
            </div>
        `;

        $('#import-stats').html(stats);

        if (results.details && results.details.length > 0) {
            let detailsHtml = '<div class="list-group">';
            results.details.forEach(function(detail) {
                let badgeClass = 'bg-secondary';
                let icon = 'ti ti-info-circle';

                if (detail.type === 'imported') {
                    badgeClass = 'bg-success';
                    icon = 'ti ti-check';
                } else if (detail.type === 'skipped') {
                    badgeClass = 'bg-warning';
                    icon = 'ti ti-alert-triangle';
                } else if (detail.type === 'error') {
                    badgeClass = 'bg-danger';
                    icon = 'ti ti-x';
                }

                detailsHtml += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="${icon}"></i> ${detail.name || 'Unknown'}
                                    <span class="badge ${badgeClass} ms-2">${detail.type}</span>
                                </h6>
                                <p class="mb-1"><small><strong>SKU:</strong> ${detail.sku || 'N/A'}</small></p>
                                ${detail.reason ? `<small class="text-muted">${detail.reason}</small>` : ''}
                                ${detail.error ? `<small class="text-danger">${detail.error}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            detailsHtml += '</div>';
            $('#import-details-content').html(detailsHtml);
        }

        $('#import-results').show();
    }

    function updateButtonStates() {
        const hasSelection = selectedItemGroups.length > 0;
        $('#preview-btn').prop('disabled', !hasSelection);
        $('#import-btn').prop('disabled', !hasSelection || previewData.length === 0);
    }

    function hidePreview() {
        $('#preview-section').hide();
        previewData = [];
        updateButtonStates();
    }
});
</script>
@endpush
