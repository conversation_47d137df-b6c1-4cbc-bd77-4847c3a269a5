/**
 * ERPNext Product Import JavaScript
 * Handles AJAX interactions for the product import form
 */

$(document).ready(function() {
    let selectedGroups = [];

    // Initialize Select2
    initializeSelect2();

    // Load Item Groups on page load
    loadItemGroups();

    // Item groups selection change
    $('#item_groups').on('change', function() {
        selectedGroups = $(this).val() || [];
        console.log('Selected groups:', selectedGroups);
        updateButtons();
        hidePreview();
    });

    // Preview button click
    $('#preview-btn').on('click', function() {
        previewProducts();
    });

    // Form submit
    $('#import-form').on('submit', function(e) {
        e.preventDefault();
        importProducts();
    });

    function initializeSelect2() {
        // Initialize Select2 for Item Groups dropdown
        $('#item_groups').select2({
            placeholder: 'Select Item Groups to import from...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4',
            closeOnSelect: false,
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }

                // Add icon for better visual
                return $('<span><i class="ti ti-folder me-2"></i>' + option.text + '</span>');
            },
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-folder me-1"></i>' + option.text + '</span>');
            }
        });
    }

    function loadItemGroups() {
        $('#loading').show();

        $.ajax({
            url: window.erpnextRoutes.itemGroups,
            type: 'GET',
            success: function(response) {
                $('#loading').hide();

                if (response.error) {
                    showError('Error: ' + response.message);
                    return;
                }

                populateItemGroups(response.data);
            },
            error: function() {
                $('#loading').hide();
                showError('Failed to load Item Groups');
            }
        });
    }

    function populateItemGroups(groups) {
        const select = $('#item_groups');

        // Clear existing options
        select.empty();

        // Add default option
        select.append(new Option('Select Item Groups...', '', false, false));

        // Add Item Groups
        groups.forEach(function(group) {
            select.append(new Option(group.label, group.value, false, false));
        });

        // Refresh Select2 and update placeholder
        select.trigger('change');
        select.select2('destroy').select2({
            placeholder: 'Select Item Groups to import from...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4',
            closeOnSelect: false,
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-folder me-2"></i>' + option.text + '</span>');
            },
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-folder me-1"></i>' + option.text + '</span>');
            }
        });

        updateButtons();
    }

    function previewProducts() {
        // Get fresh selection
        selectedGroups = $('#item_groups').val() || [];
        console.log('Preview - Selected groups:', selectedGroups);

        if (selectedGroups.length === 0) {
            showError('Please select at least one Item Group');
            return;
        }

        $('#loading').show();

        $.ajax({
            url: window.erpnextRoutes.preview,
            type: 'POST',
            data: {
                item_groups: selectedGroups,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#loading').hide();

                if (response.error) {
                    showError('Error: ' + response.message);
                    return;
                }

                displayPreview(response.data, response.total);
            },
            error: function() {
                $('#loading').hide();
                showError('Failed to preview products');
            }
        });
    }

    function displayPreview(products, total) {
        $('#preview-count').text(total + ' products');

        if (products.length === 0) {
            $('#preview-content').html('<div class="alert alert-warning">No products found.</div>');
        } else {
            let html = '<div class="table-responsive"><table class="table table-striped">';
            html += '<thead><tr><th>SKU</th><th>Name</th><th>Group</th><th>Price</th></tr></thead><tbody>';

            products.forEach(function(product) {
                const price = product.standard_rate ? '$' + parseFloat(product.standard_rate).toFixed(2) : 'N/A';
                html += '<tr>';
                html += '<td><code>' + product.item_code + '</code></td>';
                html += '<td>' + product.item_name + '</td>';
                html += '<td>' + product.item_group + '</td>';
                html += '<td>' + price + '</td>';
                html += '</tr>';
            });

            html += '</tbody></table></div>';
            $('#preview-content').html(html);
        }

        $('#preview-section').show();
        updateButtons();
    }

    function importProducts() {
        // Get fresh selection
        selectedGroups = $('#item_groups').val() || [];
        console.log('Import - Selected groups:', selectedGroups);

        if (selectedGroups.length === 0) {
            showError('Please select at least one Item Group');
            return;
        }

        $('#loading').show();
        $('#import-btn').prop('disabled', true);

        $.ajax({
            url: window.erpnextRoutes.import,
            type: 'POST',
            data: {
                item_groups: selectedGroups,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#loading').hide();
                $('#import-btn').prop('disabled', false);

                if (response.error) {
                    showError('Error: ' + response.message);
                    return;
                }

                displayResults(response.data || response);
                showSuccess('Import completed! Check results below.');
            },
            error: function() {
                $('#loading').hide();
                $('#import-btn').prop('disabled', false);
                showError('Import failed');
            }
        });
    }

    function displayResults(results) {
        let html = '<div class="row">';
        html += '<div class="col-md-3"><strong>Total:</strong> ' + (results.total || 0) + '</div>';
        html += '<div class="col-md-3"><strong>Imported:</strong> <span class="text-success">' + (results.imported || 0) + '</span></div>';
        html += '<div class="col-md-3"><strong>Skipped:</strong> <span class="text-warning">' + (results.skipped || 0) + '</span></div>';
        html += '<div class="col-md-3"><strong>Errors:</strong> <span class="text-danger">' + (results.errors || 0) + '</span></div>';
        html += '</div>';

        $('#results-content').html(html);
        $('#results').show();
    }

    function updateButtons() {
        // Get fresh selection
        const currentSelection = $('#item_groups').val() || [];
        const hasSelection = currentSelection.length > 0;

        console.log('Update buttons - Current selection:', currentSelection);

        $('#preview-btn').prop('disabled', !hasSelection);
        $('#import-btn').prop('disabled', !hasSelection);
    }

    function hidePreview() {
        $('#preview-section').hide();
        $('#results').hide();
    }

    function showSuccess(message) {
        if (typeof Botble !== 'undefined' && Botble.showSuccess) {
            Botble.showSuccess(message);
        } else {
            alert('Success: ' + message);
        }
    }

    function showError(message) {
        if (typeof Botble !== 'undefined' && Botble.showError) {
            Botble.showError(message);
        } else {
            alert('Error: ' + message);
        }
    }
});
