/**
 * ERPNext Product Import JavaScript
 * Handles AJAX interactions for the product import form
 *
 * @version 1.1.0
 * <AUTHOR>
 */

// Import jQuery if using modules (webpack will handle this)
// import $ from 'jquery';

class ERPNextProductImport {
    constructor() {
        this.selectedItemGroups = [];
        this.previewData = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadItemGroups();
    }

    bindEvents() {
        // Preview button click handler
        $(document).on('click', '#preview-btn', () => {
            this.previewProducts();
        });

        // Form submit handler
        $(document).on('submit', '#main-form', (e) => {
            e.preventDefault();
            this.importProducts();
        });

        // Item groups selection change handler
        $(document).on('change', '#item_groups_select', (e) => {
            this.selectedItemGroups = $(e.target).val() || [];
            this.updateButtonStates();
            this.hidePreview();
        });
    }

    loadItemGroups() {
        $('#item-groups-loading').show();
        $('#item-groups-error').hide();

        $.ajax({
            url: window.erpnextRoutes.itemGroups,
            type: 'GET',
            success: (response) => {
                if (response.error) {
                    this.showItemGroupsError(response.message);
                    return;
                }

                this.populateItemGroupsSelect(response.data);
                $('#item-groups-loading').hide();
            },
            error: (xhr) => {
                let message = 'Failed to load Item Groups';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                this.showItemGroupsError(message);
            }
        });
    }

    populateItemGroupsSelect(itemGroups) {
        const select = $('#item_groups_select');
        select.empty();

        itemGroups.forEach((group) => {
            select.append(new Option(group.label, group.value, false, false));
        });

        select.attr('data-placeholder', 'Select Item Groups to import from...');
        select.trigger('change');
        this.updateButtonStates();
    }

    showItemGroupsError(message) {
        $('#item-groups-loading').hide();
        $('#item-groups-error-message').text(message);
        $('#item-groups-error').show();
    }

    previewProducts() {
        if (this.selectedItemGroups.length === 0) {
            this.showError('Please select at least one Item Group');
            return;
        }

        $('#preview-loading').show();
        $('#preview-content').empty();
        $('#preview-section').show();

        $.ajax({
            url: window.erpnextRoutes.preview,
            type: 'POST',
            data: {
                item_groups: this.selectedItemGroups,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                $('#preview-loading').hide();

                if (response.error) {
                    this.showError(response.message);
                    return;
                }

                this.previewData = response.data;
                this.displayPreview(response.data, response.total);
                this.updateButtonStates();
            },
            error: (xhr) => {
                $('#preview-loading').hide();
                let message = 'Failed to preview products';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                this.showError(message);
            }
        });
    }

    displayPreview(products, total) {
        $('#preview-count').text(total + ' products');

        if (products.length === 0) {
            $('#preview-content').html('<div class="alert alert-warning">No products found in selected Item Groups.</div>');
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>SKU</th><th>Name</th><th>Item Group</th><th>Price</th><th>Status</th></tr></thead><tbody>';

        products.forEach((product) => {
            const status = product.disabled ? '<span class="badge bg-danger">Disabled</span>' : '<span class="badge bg-success">Active</span>';
            const price = product.standard_rate ? '$' + parseFloat(product.standard_rate).toFixed(2) : 'N/A';

            html += `<tr>
                <td><code>${product.item_code}</code></td>
                <td>${product.item_name}</td>
                <td>${product.item_group}</td>
                <td>${price}</td>
                <td>${status}</td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        $('#preview-content').html(html);
    }

    importProducts() {
        if (this.selectedItemGroups.length === 0) {
            this.showError('Please select at least one Item Group');
            return;
        }

        $('#import-loading').show();
        $('#import-btn').prop('disabled', true);

        $.ajax({
            url: window.erpnextRoutes.import,
            type: 'POST',
            data: {
                item_groups: this.selectedItemGroups,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                if (response.error) {
                    this.showError(response.message);
                    return;
                }

                this.displayImportResults(response.data, response.additional_data);

                if (response.additional_data.type === 'success') {
                    this.showSuccess(response.message);
                } else {
                    this.showWarning(response.message);
                }
            },
            error: (xhr) => {
                $('#import-loading').hide();
                $('#import-btn').prop('disabled', false);

                let message = 'Import failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                this.showError(message);
            }
        });
    }

    displayImportResults(results, additionalData) {
        const stats = `
            <div class="row">
                <div class="col-md-3"><strong>Total:</strong> ${results.total}</div>
                <div class="col-md-3"><strong>Imported:</strong> <span class="text-success">${results.imported}</span></div>
                <div class="col-md-3"><strong>Skipped:</strong> <span class="text-warning">${results.skipped}</span></div>
                <div class="col-md-3"><strong>Errors:</strong> <span class="text-danger">${results.errors}</span></div>
            </div>
        `;

        $('#import-stats').html(stats);

        if (results.details && results.details.length > 0) {
            let detailsHtml = '<div class="list-group">';
            results.details.forEach((detail) => {
                let badgeClass = 'bg-secondary';
                let icon = 'ti ti-info-circle';

                if (detail.type === 'imported') {
                    badgeClass = 'bg-success';
                    icon = 'ti ti-check';
                } else if (detail.type === 'skipped') {
                    badgeClass = 'bg-warning';
                    icon = 'ti ti-alert-triangle';
                } else if (detail.type === 'error') {
                    badgeClass = 'bg-danger';
                    icon = 'ti ti-x';
                }

                detailsHtml += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="${icon}"></i> ${detail.name || 'Unknown'}
                                    <span class="badge ${badgeClass} ms-2">${detail.type}</span>
                                </h6>
                                <p class="mb-1"><small><strong>SKU:</strong> ${detail.sku || 'N/A'}</small></p>
                                ${detail.reason ? `<small class="text-muted">${detail.reason}</small>` : ''}
                                ${detail.error ? `<small class="text-danger">${detail.error}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            detailsHtml += '</div>';
            $('#import-details-content').html(detailsHtml);
        }

        $('#import-results').show();
    }

    updateButtonStates() {
        const hasSelection = this.selectedItemGroups.length > 0;
        $('#preview-btn').prop('disabled', !hasSelection);
        $('#import-btn').prop('disabled', !hasSelection || this.previewData.length === 0);
    }

    hidePreview() {
        $('#preview-section').hide();
        this.previewData = [];
        this.updateButtonStates();
    }

    showSuccess(message) {
        if (typeof Botble !== 'undefined' && Botble.showSuccess) {
            Botble.showSuccess(message);
        } else {
            alert('Success: ' + message);
        }
    }

    showError(message) {
        if (typeof Botble !== 'undefined' && Botble.showError) {
            Botble.showError(message);
        } else {
            alert('Error: ' + message);
        }
    }

    showWarning(message) {
        if (typeof Botble !== 'undefined' && Botble.showWarning) {
            Botble.showWarning(message);
        } else {
            alert('Warning: ' + message);
        }
    }
}

// Initialize when document is ready
$(document).ready(() => {
    if (typeof window.erpnextRoutes !== 'undefined') {
        new ERPNextProductImport();
    }
});
