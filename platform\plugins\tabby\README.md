# Tabby Pay-in-4 Payment Gateway Plugin for Botble

A comprehensive Tabby Pay-in-4 payment gateway integration for Botble e-commerce platform, allowing customers to split their purchases into 4 interest-free installments.

## Features

- **Pay-in-4 Installments**: Customers can split purchases into 4 payments with no interest or fees
- **Real-time Eligibility Check**: Pre-scoring to determine customer eligibility before showing payment option
- **Automatic Payment Processing**: Seamless integration with Botble's payment system
- **Webhook Support**: Real-time payment status updates via webhooks
- **Refund Support**: Full and partial refund capabilities
- **Multi-currency Support**: Supports AED, SAR, KWD, BHD, QAR, and EGP
- **Test & Live Environments**: Sandbox mode for testing, live mode for production
- **Mobile Optimized**: Responsive checkout experience
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Installation

1. **Extract Plugin Files**
   ```bash
   # Place the plugin files in the plugins directory
   cp -r tabby/ platform/plugins/
   ```

2. **Activate Plugin**
   - Go to Admin → Plugins
   - Find "Tabby Pay-in-4 Payment Gateway"
   - Click "Activate"

## Configuration

### 1. Get Tabby Credentials

1. Register for a Tabby merchant account at [https://merchant.tabby.ai/](https://merchant.tabby.ai/)
2. Complete your application and verification process
3. Access your Tabby Merchant Dashboard
4. Retrieve your API credentials:
   - Public Key
   - Secret Key
   - Merchant Code

### 2. Configure Plugin Settings

1. Go to Admin → Plugins → Payment Methods
2. Find "Tabby Pay-in-4" section
3. Fill in your credentials:
   - **Public Key**: Your Tabby public API key
   - **Secret Key**: Your Tabby secret API key
   - **Merchant Code**: Your Tabby merchant code
   - **Environment**: Select "Sandbox" for testing, "Live" for production
4. Enable the payment method
5. Save settings

### 3. Webhook Configuration

The plugin automatically provides a webhook URL at:
```
https://yourdomain.com/payment/tabby/webhook
```

Register this URL in your Tabby Merchant Dashboard under Webhooks settings.

## Testing

### Test Credentials

Use Tabby's test credentials for sandbox testing:
- Use the sandbox environment setting
- Test with various amounts to verify eligibility checks
- Test different scenarios (success, cancellation, failure)

### Test Scenarios

1. **Successful Payment**
   - Add items to cart
   - Proceed to checkout
   - Select "Tabby Pay-in-4"
   - Complete payment on Tabby's hosted page
   - Verify order completion

2. **Eligibility Rejection**
   - Test with amounts outside Tabby's limits
   - Verify appropriate rejection messages are shown

3. **Payment Cancellation**
   - Start payment process
   - Cancel on Tabby's page
   - Verify proper handling of cancellation

4. **Webhook Testing**
   - Use tools like webhook.site for testing
   - Verify webhook events are received and processed

## API Integration Details

### Checkout Flow

1. **Pre-scoring Check**: Background eligibility verification
2. **Session Creation**: Create Tabby checkout session
3. **Customer Redirect**: Redirect to Tabby hosted payment page
4. **Payment Processing**: Handle customer return and webhook notifications
5. **Payment Capture**: Automatically capture authorized payments
6. **Order Fulfillment**: Complete order processing

### Supported Payment Statuses

- `CREATED`: Payment session created
- `AUTHORIZED`: Payment authorized by customer
- `CLOSED`: Payment captured and completed
- `REJECTED`: Payment rejected by Tabby
- `EXPIRED`: Payment session expired

### Error Handling

The plugin handles various error scenarios:
- API connection failures
- Invalid credentials
- Currency not supported
- Amount limits exceeded
- Customer eligibility issues

## Webhook Events

The plugin handles these webhook events:
- `authorized`: Payment authorized by customer
- `closed`: Payment captured and completed
- `rejected`: Payment rejected
- `expired`: Payment session expired

## Refunds

Refunds can be processed through:
1. Botble admin panel (if integrated with order management)
2. Direct API calls using the TabbyPaymentService

```php
$tabbyService = new TabbyPaymentService();
$result = $tabbyService->refundPayment($paymentId, $amount, $reason);
```

## Troubleshooting

### Common Issues

1. **Payment Method Not Showing**
   - Verify plugin is activated
   - Check API credentials are correct
   - Ensure currency is supported
   - Check eligibility requirements

2. **Webhook Not Working**
   - Verify webhook URL is registered in Tabby dashboard
   - Check server logs for errors
   - Ensure SSL certificate is valid
   - Verify webhook signature validation

3. **Payment Failures**
   - Check API credentials
   - Verify environment setting (sandbox vs live)
   - Review Tabby dashboard for payment details
   - Check server logs for API errors

### Debug Mode

Enable debug logging by adding to your `.env`:
```
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed error information.

## Security Considerations

- Always use HTTPS in production
- Validate webhook signatures if configured
- Store API credentials securely
- Regularly update the plugin
- Monitor payment transactions

## Support

- **Tabby Support**: <EMAIL>
- **Documentation**: https://docs.tabby.ai/
- **Botble Support**: https://botble.com/contact

## Changelog

### Version 1.0.0
- Initial release
- Complete Tabby Pay-in-4 integration
- Pre-scoring and eligibility checks
- Webhook support
- Refund capabilities
- Multi-currency support
- Comprehensive error handling

## License

This plugin is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Requirements

- PHP 8.1+
- Botble CMS 7.3.0+
- GuzzleHTTP 7.0+
- Valid SSL certificate (for production)
- Tabby merchant account
